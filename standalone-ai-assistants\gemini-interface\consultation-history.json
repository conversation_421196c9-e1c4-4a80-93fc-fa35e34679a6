[{"timestamp": "2025-07-06T11:23:53.334012", "query": "كيف أحسن تنظيم المشروع؟", "response": "أنصح بتقسيم المشروع إلى وحدات منفصلة، كل وحدة لها مسؤولية محددة. استخدم مجلدات واضحة وملفات README شاملة.", "type": "simulated", "session_id": "20250706_1123"}, {"timestamp": "2025-07-06T12:46:57.535362", "query": "كيف أحسن تنظيم المشروع؟", "response": "أنصح بتقسيم المشروع إلى وحدات منفصلة، كل وحدة لها مسؤولية محددة. استخدم مجلدات واضحة وملفات README شاملة.", "type": "simulated", "session_id": "20250706_1246"}, {"timestamp": "2025-07-07T01:36:18.997943", "query": "كيف أحسن تنظيم المشروع؟", "response": "أنصح بتقسيم المشروع إلى وحدات منفصلة، كل وحدة لها مسؤولية محددة. استخدم مجلدات واضحة وملفات README شاملة.", "type": "simulated", "session_id": "20250707_0136"}, {"timestamp": "2025-07-07T09:01:20.334881", "query": "كيف أحسن تنظيم المشروع؟", "response": "أنصح بتقسيم المشروع إلى وحدات منفصلة، كل وحدة لها مسؤولية محددة. استخدم مجلدات واضحة وملفات README شاملة.", "type": "simulated", "session_id": "20250707_0901"}, {"timestamp": "2025-07-07T09:03:39.413650", "query": "كيف أحسن تنظيم المشروع؟", "response": "أنصح بتقسيم المشروع إلى وحدات منفصلة، كل وحدة لها مسؤولية محددة. استخدم مجلدات واضحة وملفات README شاملة.", "type": "simulated", "session_id": "20250707_0903"}, {"timestamp": "2025-07-07T09:26:59.958435", "query": "كيف أحسن تنظيم المشروع؟", "response": "أنصح بتقسيم المشروع إلى وحدات منفصلة، كل وحدة لها مسؤولية محددة. استخدم مجلدات واضحة وملفات README شاملة.", "type": "simulated", "session_id": "20250707_0926"}, {"timestamp": "2025-07-07T09:30:30.753111", "query": "كيف أحسن تنظيم المشروع؟", "response": "أنصح بتقسيم المشروع إلى وحدات منفصلة، كل وحدة لها مسؤولية محددة. استخدم مجلدات واضحة وملفات README شاملة.", "type": "simulated", "session_id": "20250707_0930"}]