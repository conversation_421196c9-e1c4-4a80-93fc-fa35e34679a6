#!/usr/bin/env python3
"""
نظام الذكاء الاصطناعي السريع
AI Coordinator + Ollama + Memory + AnythingLLM + n8n
"""

import requests
import json
import time
from datetime import datetime

class QuickAISystem:
    """نظام الذكاء الاصطناعي السريع"""
    
    def __init__(self):
        self.services = {
            "ai_coordinator": "http://localhost:4003",
            "ollama": "http://localhost:11434", 
            "anythingllm": "http://localhost:4001",
            "n8n": "http://localhost:4002"
        }
        self.memory = {}
        
    def check_services(self):
        """فحص حالة الخدمات"""
        print("🔍 فحص حالة الخدمات...")
        status = {}
        
        for service, url in self.services.items():
            try:
                if service == "ai_coordinator":
                    response = requests.get(f"{url}/api/health", timeout=5)
                elif service == "ollama":
                    response = requests.get(f"{url}/api/tags", timeout=5)
                elif service == "anythingllm":
                    response = requests.get(f"{url}/api/v1/system/ping", timeout=5)
                elif service == "n8n":
                    response = requests.get(f"{url}/healthz", timeout=5)
                
                if response.status_code == 200:
                    status[service] = "✅ متصل"
                else:
                    status[service] = f"⚠️ خطأ {response.status_code}"
            except Exception as e:
                status[service] = f"❌ غير متصل"
        
        return status
    
    def get_ollama_models(self):
        """الحصول على نماذج Ollama"""
        try:
            response = requests.get(f"{self.services['ollama']}/api/tags")
            if response.status_code == 200:
                models = response.json().get('models', [])
                return [model['name'] for model in models]
            return []
        except:
            return []
    
    def chat_with_ollama(self, model, message):
        """محادثة مع Ollama"""
        try:
            data = {
                "model": model,
                "prompt": message,
                "stream": False
            }
            response = requests.post(
                f"{self.services['ollama']}/api/generate",
                json=data,
                timeout=30
            )
            if response.status_code == 200:
                return response.json().get('response', 'لا توجد استجابة')
            return f"خطأ: {response.status_code}"
        except Exception as e:
            return f"خطأ في الاتصال: {str(e)}"
    
    def coordinate_ai_task(self, task_description):
        """تنسيق مهمة ذكية"""
        try:
            data = {
                "task": task_description,
                "timestamp": datetime.now().isoformat(),
                "services": ["ollama", "anythingllm"]
            }
            response = requests.post(
                f"{self.services['ai_coordinator']}/api/coordinate",
                json=data,
                timeout=15
            )
            if response.status_code == 200:
                return response.json()
            return {"error": f"خطأ {response.status_code}"}
        except Exception as e:
            return {"error": f"خطأ في التنسيق: {str(e)}"}
    
    def save_to_memory(self, key, value):
        """حفظ في الذاكرة"""
        self.memory[key] = {
            "value": value,
            "timestamp": datetime.now().isoformat()
        }
        
        # حفظ في ملف
        memory_file = "shared-memory/quick-session.json"
        try:
            with open(memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.memory, f, ensure_ascii=False, indent=2)
        except:
            pass
    
    def get_from_memory(self, key):
        """استرجاع من الذاكرة"""
        return self.memory.get(key, {}).get('value', None)
    
    def run_quick_demo(self):
        """تشغيل عرض سريع"""
        print("🚀 نظام الذكاء الاصطناعي السريع")
        print("=" * 50)
        
        # فحص الخدمات
        status = self.check_services()
        print("\n📊 حالة الخدمات:")
        for service, state in status.items():
            print(f"  {service}: {state}")
        
        # فحص نماذج Ollama
        models = self.get_ollama_models()
        print(f"\n🤖 نماذج Ollama المتاحة ({len(models)}):")
        for model in models[:4]:  # أول 4 نماذج
            print(f"  • {model}")
        
        # اختبار سريع
        if models:
            print(f"\n💬 اختبار سريع مع {models[0]}:")
            test_message = "مرحبا، كيف يمكنني مساعدتك اليوم؟"
            response = self.chat_with_ollama(models[0], test_message)
            print(f"  السؤال: {test_message}")
            print(f"  الجواب: {response[:100]}...")
            
            # حفظ في الذاكرة
            self.save_to_memory("last_test", {
                "model": models[0],
                "question": test_message,
                "answer": response,
                "timestamp": datetime.now().isoformat()
            })
        
        # اختبار AI Coordinator
        if status.get("ai_coordinator") == "✅ متصل":
            print(f"\n🎯 اختبار AI Coordinator:")
            task = "تحليل النص وتلخيصه"
            result = self.coordinate_ai_task(task)
            print(f"  المهمة: {task}")
            print(f"  النتيجة: {str(result)[:100]}...")
        
        print(f"\n✅ النظام جاهز للاستخدام!")
        return True
    
    def interactive_mode(self):
        """وضع التفاعل"""
        print("\n🎮 الوضع التفاعلي")
        print("اكتب 'exit' للخروج")
        print("-" * 30)
        
        models = self.get_ollama_models()
        if not models:
            print("❌ لا توجد نماذج متاحة")
            return
        
        current_model = models[0]
        print(f"🤖 النموذج الحالي: {current_model}")
        
        while True:
            try:
                user_input = input("\n💬 أنت: ").strip()
                
                if user_input.lower() in ['exit', 'خروج', 'quit']:
                    print("👋 وداعاً!")
                    break
                
                if user_input.startswith('/model '):
                    new_model = user_input[7:].strip()
                    if new_model in models:
                        current_model = new_model
                        print(f"✅ تم تغيير النموذج إلى: {current_model}")
                    else:
                        print(f"❌ النموذج غير موجود. المتاح: {', '.join(models)}")
                    continue
                
                if user_input.startswith('/coordinate '):
                    task = user_input[12:].strip()
                    result = self.coordinate_ai_task(task)
                    print(f"🎯 AI Coordinator: {result}")
                    continue
                
                if user_input:
                    print(f"🤖 {current_model}: ", end="", flush=True)
                    response = self.chat_with_ollama(current_model, user_input)
                    print(response)
                    
                    # حفظ المحادثة
                    self.save_to_memory(f"chat_{datetime.now().strftime('%H%M%S')}", {
                        "user": user_input,
                        "ai": response,
                        "model": current_model
                    })
                
            except KeyboardInterrupt:
                print("\n👋 تم الإيقاف")
                break
            except Exception as e:
                print(f"❌ خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    ai_system = QuickAISystem()
    
    # تشغيل العرض السريع
    ai_system.run_quick_demo()
    
    # السؤال عن الوضع التفاعلي
    choice = input("\n🎮 هل تريد الدخول للوضع التفاعلي؟ (y/n): ").strip().lower()
    if choice in ['y', 'yes', 'نعم']:
        ai_system.interactive_mode()
    
    print("\n📊 ملخص الجلسة:")
    print(f"  • الذاكرة: {len(ai_system.memory)} عنصر")
    print(f"  • الوقت: {datetime.now().strftime('%H:%M:%S')}")
    print("  • الحالة: مكتملة ✅")
    
    return ai_system

if __name__ == "__main__":
    system = main()
