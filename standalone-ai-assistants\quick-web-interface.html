<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الذكاء الاصطناعي السريع</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        
        .service-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #2196F3;
        }
        
        .service-card.active {
            border-color: #4CAF50;
            background: #f1f8e9;
        }
        
        .service-card.error {
            border-color: #f44336;
            background: #ffebee;
        }
        
        .service-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .service-status {
            font-size: 1.1em;
            margin-bottom: 15px;
        }
        
        .service-url {
            background: #e3f2fd;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
            margin-bottom: 15px;
        }
        
        .service-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s;
        }
        
        .service-btn:hover {
            background: #1976D2;
        }
        
        .chat-section {
            margin: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }
        
        .chat-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1.1em;
            margin-bottom: 15px;
        }
        
        .chat-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .chat-response {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }
        .status-loading { background: #FF9800; animation: pulse 1s infinite; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .quick-btn {
            background: #FF9800;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 نظام الذكاء الاصطناعي السريع</h1>
            <p>AI Coordinator + Ollama + Memory + AnythingLLM + n8n</p>
        </div>
        
        <div class="services-grid">
            <div class="service-card" id="ai-coordinator-card">
                <div class="service-title">🎯 AI Coordinator</div>
                <div class="service-status">
                    <span id="ai-coordinator-status">فحص...</span>
                    <span class="status-indicator status-loading" id="ai-coordinator-indicator"></span>
                </div>
                <div class="service-url">http://localhost:4003</div>
                <button class="service-btn" onclick="openService('http://localhost:4003')">فتح الخدمة</button>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="testCoordinator()">اختبار سريع</button>
                </div>
            </div>
            
            <div class="service-card" id="ollama-card">
                <div class="service-title">🤖 Ollama Models</div>
                <div class="service-status">
                    <span id="ollama-status">فحص...</span>
                    <span class="status-indicator status-loading" id="ollama-indicator"></span>
                </div>
                <div class="service-url">http://localhost:11434</div>
                <button class="service-btn" onclick="openService('http://localhost:11434')">فتح الخدمة</button>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="listModels()">عرض النماذج</button>
                </div>
            </div>
            
            <div class="service-card" id="anythingllm-card">
                <div class="service-title">📚 AnythingLLM</div>
                <div class="service-status">
                    <span id="anythingllm-status">فحص...</span>
                    <span class="status-indicator status-loading" id="anythingllm-indicator"></span>
                </div>
                <div class="service-url">http://localhost:4001</div>
                <button class="service-btn" onclick="openService('http://localhost:4001')">فتح الخدمة</button>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="testAnythingLLM()">اختبار الاتصال</button>
                </div>
            </div>
            
            <div class="service-card" id="n8n-card">
                <div class="service-title">⚡ n8n Automation</div>
                <div class="service-status">
                    <span id="n8n-status">فحص...</span>
                    <span class="status-indicator status-loading" id="n8n-indicator"></span>
                </div>
                <div class="service-url">http://localhost:4002</div>
                <button class="service-btn" onclick="openService('http://localhost:4002')">فتح الخدمة</button>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="testN8N()">فحص الحالة</button>
                </div>
            </div>
        </div>
        
        <div class="chat-section">
            <h3>💬 محادثة سريعة مع Ollama</h3>
            <input type="text" class="chat-input" id="chatInput" placeholder="اكتب رسالتك هنا..." />
            <button class="chat-btn" onclick="sendMessage()">إرسال</button>
            <button class="chat-btn" onclick="clearChat()" style="background: #f44336;">مسح</button>
            <div class="chat-response" id="chatResponse">جاهز للمحادثة...</div>
        </div>
    </div>

    <script>
        // فحص حالة الخدمات
        async function checkServices() {
            const services = [
                { name: 'ai-coordinator', url: 'http://localhost:4003/api/health' },
                { name: 'ollama', url: 'http://localhost:11434/api/tags' },
                { name: 'anythingllm', url: 'http://localhost:4001/api/v1/system/ping' },
                { name: 'n8n', url: 'http://localhost:4002/healthz' }
            ];
            
            for (const service of services) {
                try {
                    const response = await fetch(service.url);
                    updateServiceStatus(service.name, response.ok);
                } catch (error) {
                    updateServiceStatus(service.name, false);
                }
            }
        }
        
        function updateServiceStatus(serviceName, isOnline) {
            const statusElement = document.getElementById(`${serviceName}-status`);
            const indicatorElement = document.getElementById(`${serviceName}-indicator`);
            const cardElement = document.getElementById(`${serviceName}-card`);
            
            if (isOnline) {
                statusElement.textContent = 'متصل ✅';
                indicatorElement.className = 'status-indicator status-online';
                cardElement.className = 'service-card active';
            } else {
                statusElement.textContent = 'غير متصل ❌';
                indicatorElement.className = 'status-indicator status-offline';
                cardElement.className = 'service-card error';
            }
        }
        
        function openService(url) {
            window.open(url, '_blank');
        }
        
        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const response = document.getElementById('chatResponse');
            const message = input.value.trim();
            
            if (!message) return;
            
            response.textContent = 'جاري الإرسال...';
            
            try {
                const result = await fetch('http://localhost:11434/api/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: 'llama3.2:1b',
                        prompt: message,
                        stream: false
                    })
                });
                
                const data = await result.json();
                response.textContent = `أنت: ${message}\n\nالذكاء الاصطناعي: ${data.response || 'لا توجد استجابة'}`;
            } catch (error) {
                response.textContent = `خطأ: ${error.message}`;
            }
            
            input.value = '';
        }
        
        function clearChat() {
            document.getElementById('chatResponse').textContent = 'جاهز للمحادثة...';
        }
        
        // اختبارات سريعة
        async function testCoordinator() {
            alert('اختبار AI Coordinator...');
        }
        
        async function listModels() {
            try {
                const response = await fetch('http://localhost:11434/api/tags');
                const data = await response.json();
                const models = data.models.map(m => m.name).join('\n');
                alert(`النماذج المتاحة:\n${models}`);
            } catch (error) {
                alert(`خطأ: ${error.message}`);
            }
        }
        
        async function testAnythingLLM() {
            alert('اختبار AnythingLLM...');
        }
        
        async function testN8N() {
            alert('اختبار n8n...');
        }
        
        // تشغيل فحص الخدمات عند التحميل
        window.onload = function() {
            checkServices();
            setInterval(checkServices, 30000); // فحص كل 30 ثانية
        };
        
        // إرسال بالضغط على Enter
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
