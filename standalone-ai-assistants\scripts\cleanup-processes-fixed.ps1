# Enhanced Background Process Cleanup Script
# ==========================================

param(
    [switch]$Force,
    [switch]$DryRun,
    [switch]$KeepVSCode,
    [switch]$Verbose
)

# Set UTF-8 encoding
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🧹 Enhanced Background Process Cleanup" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

# Get current VS Code PID
$currentVSCodePID = $null
if ($env:VSCODE_PID) {
    $currentVSCodePID = $env:VSCODE_PID
    Write-Host "🔒 Protected VS Code PID: $currentVSCodePID" -ForegroundColor Green
}

# Initialize counters
$processStats = @{
    NodeProtected = 0
    NodeToKill = 0
    VSCodeToKill = 0
    PowerShellToKill = 0
    OtherToKill = 0
    TotalKilled = 0
    Errors = 0
}

# Function to safely get command line
function Get-SafeCommandLine {
    param($ProcessId)
    try {
        $wmi = Get-WmiObject Win32_Process -Filter "ProcessId = $ProcessId" -ErrorAction SilentlyContinue
        if ($wmi) {
            return $wmi.CommandLine
        }
        return "Unknown"
    }
    catch {
        return "Access Denied"
    }
}

# Function to format process info
function Format-ProcessInfo {
    param($Process, $CommandLine, $Action, $Color)
    $shortCmd = if ($CommandLine -and $CommandLine.Length -gt 60) { 
        $CommandLine.Substring(0, 60) + "..." 
    } else { 
        $CommandLine 
    }
    Write-Host "   $Action PID $($Process.Id) - $shortCmd" -ForegroundColor $Color
}

# 1. Check Node.js processes
Write-Host "`n🟡 Analyzing Node.js processes..." -ForegroundColor Yellow

$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
$criticalNodeProcesses = @()
$safeToKillNodeProcesses = @()

foreach ($proc in $nodeProcesses) {
    $commandLine = Get-SafeCommandLine -ProcessId $proc.Id
    
    # Critical processes to keep
    if ($commandLine -match "anythingllm|ollama|n8n|ai-coordinator") {
        $criticalNodeProcesses += $proc
        $processStats.NodeProtected++
        if ($Verbose) {
            Format-ProcessInfo -Process $proc -CommandLine $commandLine -Action "🔒 PROTECTED:" -Color "Green"
        }
    }
    # Safe to kill processes
    elseif ($commandLine -match "nodemon|webpack|vite|dev-server|test|npm-cache") {
        $safeToKillNodeProcesses += $proc
        $processStats.NodeToKill++
        if ($Verbose) {
            Format-ProcessInfo -Process $proc -CommandLine $commandLine -Action "❌ TO KILL:" -Color "Red"
        }
    }
    else {
        if ($Verbose) {
            Format-ProcessInfo -Process $proc -CommandLine $commandLine -Action "⚠️  UNKNOWN:" -Color "Yellow"
        }
    }
}

Write-Host "   Found: $($nodeProcesses.Count) total, $($processStats.NodeProtected) protected, $($processStats.NodeToKill) to kill" -ForegroundColor White

# 2. Check VS Code processes
Write-Host "`n🟡 Analyzing VS Code processes..." -ForegroundColor Yellow

$codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
$safeToKillCodeProcesses = @()

foreach ($proc in $codeProcesses) {
    $memoryMB = [math]::Round($proc.WorkingSet / 1MB, 1)
    
    # Skip current VS Code or if KeepVSCode is set
    if ($proc.Id -eq $currentVSCodePID -or $KeepVSCode) {
        if ($Verbose) {
            Write-Host "   🔒 PROTECTED: VS Code PID $($proc.Id) ($memoryMB MB)" -ForegroundColor Green
        }
    }
    # Small helper processes safe to kill
    elseif ($proc.WorkingSet -lt 100MB) {
        $safeToKillCodeProcesses += $proc
        $processStats.VSCodeToKill++
        if ($Verbose) {
            Write-Host "   ❌ TO KILL: VS Code Helper PID $($proc.Id) ($memoryMB MB)" -ForegroundColor Red
        }
    }
    else {
        if ($Verbose) {
            Write-Host "   ⚠️  LARGE: VS Code PID $($proc.Id) ($memoryMB MB)" -ForegroundColor Yellow
        }
    }
}

Write-Host "   Found: $($codeProcesses.Count) total, $($processStats.VSCodeToKill) helpers to kill" -ForegroundColor White

# 3. Check PowerShell processes
Write-Host "`n🟡 Analyzing PowerShell processes..." -ForegroundColor Yellow

$psProcesses = Get-Process -Name "powershell" -ErrorAction SilentlyContinue | Where-Object { $_.Id -ne $PID }
$processStats.PowerShellToKill = $psProcesses.Count

if ($Verbose) {
    foreach ($proc in $psProcesses) {
        Write-Host "   ❌ TO KILL: PowerShell PID $($proc.Id)" -ForegroundColor Red
    }
}

Write-Host "   Found: $($psProcesses.Count) extra PowerShell processes to kill" -ForegroundColor White

# 4. Check other processes
Write-Host "`n🟡 Analyzing other processes..." -ForegroundColor Yellow

$otherProcesses = @()
$processesToCheck = @("code-tunnel", "cloudcode_cli")

foreach ($processName in $processesToCheck) {
    $procs = Get-Process -Name $processName -ErrorAction SilentlyContinue
    foreach ($proc in $procs) {
        $otherProcesses += $proc
        $processStats.OtherToKill++
        if ($Verbose) {
            Write-Host "   ❌ TO KILL: $processName PID $($proc.Id)" -ForegroundColor Red
        }
    }
}

Write-Host "   Found: $($otherProcesses.Count) other processes to kill" -ForegroundColor White

# Summary
$totalToKill = $processStats.NodeToKill + $processStats.VSCodeToKill + $processStats.PowerShellToKill + $processStats.OtherToKill

Write-Host "`n📊 CLEANUP SUMMARY:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host "🔒 Node.js Protected: $($processStats.NodeProtected)" -ForegroundColor Green
Write-Host "❌ Node.js To Kill: $($processStats.NodeToKill)" -ForegroundColor Red
Write-Host "❌ VS Code Helpers To Kill: $($processStats.VSCodeToKill)" -ForegroundColor Red
Write-Host "❌ PowerShell To Kill: $($processStats.PowerShellToKill)" -ForegroundColor Red
Write-Host "❌ Other To Kill: $($processStats.OtherToKill)" -ForegroundColor Red
Write-Host "📈 TOTAL TO KILL: $totalToKill" -ForegroundColor Yellow

if ($totalToKill -eq 0) {
    Write-Host "`n✅ No processes need to be killed!" -ForegroundColor Green
    exit 0
}

# Confirmation
if (-not $DryRun -and -not $Force) {
    Write-Host "`n⚠️  Do you want to proceed? (y/N): " -ForegroundColor Yellow -NoNewline
    $confirmation = Read-Host
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-Host "❌ Operation cancelled" -ForegroundColor Red
        exit 1
    }
}

if ($DryRun) {
    Write-Host "`n🔍 DRY RUN MODE - No processes will be killed" -ForegroundColor Yellow
    exit 0
}

# Kill processes
Write-Host "`n🚀 Starting process cleanup..." -ForegroundColor Yellow

# Kill safe Node.js processes
foreach ($proc in $safeToKillNodeProcesses) {
    try {
        Stop-Process -Id $proc.Id -Force -ErrorAction Stop
        Write-Host "   ✅ Killed Node.js PID $($proc.Id)" -ForegroundColor Green
        $processStats.TotalKilled++
    }
    catch {
        Write-Host "   ❌ Failed to kill Node.js PID $($proc.Id): $($_.Exception.Message)" -ForegroundColor Red
        $processStats.Errors++
    }
}

# Kill VS Code helpers
foreach ($proc in $safeToKillCodeProcesses) {
    try {
        Stop-Process -Id $proc.Id -Force -ErrorAction Stop
        Write-Host "   ✅ Killed VS Code Helper PID $($proc.Id)" -ForegroundColor Green
        $processStats.TotalKilled++
    }
    catch {
        Write-Host "   ❌ Failed to kill VS Code Helper PID $($proc.Id): $($_.Exception.Message)" -ForegroundColor Red
        $processStats.Errors++
    }
}

# Kill extra PowerShell processes
foreach ($proc in $psProcesses) {
    try {
        Stop-Process -Id $proc.Id -Force -ErrorAction Stop
        Write-Host "   ✅ Killed PowerShell PID $($proc.Id)" -ForegroundColor Green
        $processStats.TotalKilled++
    }
    catch {
        Write-Host "   ❌ Failed to kill PowerShell PID $($proc.Id): $($_.Exception.Message)" -ForegroundColor Red
        $processStats.Errors++
    }
}

# Kill other processes
foreach ($proc in $otherProcesses) {
    try {
        Stop-Process -Id $proc.Id -Force -ErrorAction Stop
        Write-Host "   ✅ Killed $($proc.ProcessName) PID $($proc.Id)" -ForegroundColor Green
        $processStats.TotalKilled++
    }
    catch {
        Write-Host "   ❌ Failed to kill $($proc.ProcessName) PID $($proc.Id): $($_.Exception.Message)" -ForegroundColor Red
        $processStats.Errors++
    }
}

Write-Host "`n🎉 CLEANUP COMPLETED!" -ForegroundColor Green
Write-Host "Successfully killed: $($processStats.TotalKilled) of $totalToKill processes" -ForegroundColor Cyan
if ($processStats.Errors -gt 0) {
    Write-Host "Errors encountered: $($processStats.Errors)" -ForegroundColor Red
}

# Memory usage after cleanup
Write-Host "`n💾 Memory usage after cleanup:" -ForegroundColor Cyan
try {
    $memoryInfo = Get-WmiObject -Class Win32_OperatingSystem
    $totalMemory = [math]::Round($memoryInfo.TotalVisibleMemorySize / 1MB, 2)
    $freeMemory = [math]::Round($memoryInfo.FreePhysicalMemory / 1MB, 2)
    $usedMemory = $totalMemory - $freeMemory
    $memoryUsagePercent = [math]::Round(($usedMemory / $totalMemory) * 100, 1)
    
    Write-Host "Used: $usedMemory GB of $totalMemory GB ($memoryUsagePercent%)" -ForegroundColor White
}
catch {
    Write-Host "Could not retrieve memory information" -ForegroundColor Yellow
}

Write-Host "`n✅ Process cleanup completed successfully!" -ForegroundColor Green
