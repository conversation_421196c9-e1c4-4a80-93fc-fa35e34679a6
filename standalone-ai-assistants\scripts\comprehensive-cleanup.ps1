# Comprehensive System Cleanup Script
# ===================================

param(
    [switch]$Force,
    [switch]$DryRun,
    [switch]$KeepVSCode,
    [switch]$CleanNPM,
    [switch]$RestartVSCode
)

# Set UTF-8 encoding
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🧹 Comprehensive System Cleanup" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

$cleanupStats = @{
    NodeKilled = 0
    VSCodeKilled = 0
    PowerShellKilled = 0
    OtherKilled = 0
    NPMCacheCleared = $false
    VSCodeRestarted = $false
    Errors = @()
}

# Function to safely kill process
function Safe-KillProcess {
    param($Process, $Description)
    try {
        Stop-Process -Id $Process.Id -Force -ErrorAction Stop
        Write-Host "   ✅ Killed $Description PID $($Process.Id)" -ForegroundColor Green
        return $true
    }
    catch {
        $error = "Failed to kill $Description PID $($Process.Id): $($_.Exception.Message)"
        Write-Host "   ❌ $error" -ForegroundColor Red
        $cleanupStats.Errors += $error
        return $false
    }
}

# 1. Clean npm cache if requested
if ($CleanNPM) {
    Write-Host "`n🟡 Cleaning npm cache..." -ForegroundColor Yellow
    if (-not $DryRun) {
        try {
            $npmResult = & npm cache clean --force 2>&1
            Write-Host "   ✅ npm cache cleaned successfully" -ForegroundColor Green
            $cleanupStats.NPMCacheCleared = $true
        }
        catch {
            Write-Host "   ❌ Failed to clean npm cache: $($_.Exception.Message)" -ForegroundColor Red
            $cleanupStats.Errors += "npm cache clean failed"
        }
    } else {
        Write-Host "   🔍 DRY RUN: Would clean npm cache" -ForegroundColor Yellow
    }
}

# 2. Kill npm-cache and unknown Node.js processes
Write-Host "`n🟡 Cleaning Node.js processes..." -ForegroundColor Yellow

$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
$killedNode = 0

foreach ($proc in $nodeProcesses) {
    try {
        $wmi = Get-WmiObject Win32_Process -Filter "ProcessId = $($proc.Id)" -ErrorAction SilentlyContinue
        $cmdLine = if ($wmi) { $wmi.CommandLine } else { "" }
        
        # Skip critical processes
        if ($cmdLine -match "anythingllm|ollama|n8n|ai-coordinator") {
            Write-Host "   🔒 PROTECTED: PID $($proc.Id) (critical service)" -ForegroundColor Green
            continue
        }
        
        # Kill npm-cache, dev tools, and unknown processes
        if ($cmdLine -match "npm-cache|_npx|nodemon|webpack|vite|dev-server|test|@modelcontextprotocol") {
            if (-not $DryRun) {
                if (Safe-KillProcess -Process $proc -Description "Node.js") {
                    $killedNode++
                }
            } else {
                Write-Host "   🔍 DRY RUN: Would kill Node.js PID $($proc.Id)" -ForegroundColor Yellow
            }
        }
        # Kill unknown processes with small memory footprint
        elseif ($proc.WorkingSet -lt 10MB -and $cmdLine -notmatch "language-server") {
            if (-not $DryRun) {
                if (Safe-KillProcess -Process $proc -Description "Node.js (small)") {
                    $killedNode++
                }
            } else {
                Write-Host "   🔍 DRY RUN: Would kill small Node.js PID $($proc.Id)" -ForegroundColor Yellow
            }
        }
        else {
            Write-Host "   ⚠️  KEPT: PID $($proc.Id) ($(($proc.WorkingSet/1MB).ToString('F1')) MB)" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "   ❌ Error processing Node.js PID $($proc.Id): $($_.Exception.Message)" -ForegroundColor Red
    }
}

$cleanupStats.NodeKilled = $killedNode
Write-Host "   📊 Node.js processes killed: $killedNode" -ForegroundColor White

# 3. Clean VS Code helper processes
if (-not $KeepVSCode) {
    Write-Host "`n🟡 Cleaning VS Code helper processes..." -ForegroundColor Yellow
    
    $codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
    $killedVSCode = 0
    $currentVSCodePID = $env:VSCODE_PID
    
    foreach ($proc in $codeProcesses) {
        # Skip current VS Code main process
        if ($proc.Id -eq $currentVSCodePID) {
            Write-Host "   🔒 PROTECTED: Current VS Code PID $($proc.Id)" -ForegroundColor Green
            continue
        }
        
        # Kill small helper processes
        if ($proc.WorkingSet -lt 100MB) {
            if (-not $DryRun) {
                if (Safe-KillProcess -Process $proc -Description "VS Code Helper") {
                    $killedVSCode++
                }
            } else {
                Write-Host "   🔍 DRY RUN: Would kill VS Code Helper PID $($proc.Id)" -ForegroundColor Yellow
            }
        }
        else {
            Write-Host "   ⚠️  KEPT: Large VS Code PID $($proc.Id) ($(($proc.WorkingSet/1MB).ToString('F1')) MB)" -ForegroundColor Yellow
        }
    }
    
    $cleanupStats.VSCodeKilled = $killedVSCode
    Write-Host "   📊 VS Code helpers killed: $killedVSCode" -ForegroundColor White
}

# 4. Clean extra PowerShell processes
Write-Host "`n🟡 Cleaning PowerShell processes..." -ForegroundColor Yellow

$psProcesses = Get-Process -Name "powershell" -ErrorAction SilentlyContinue | Where-Object { $_.Id -ne $PID }
$killedPS = 0

foreach ($proc in $psProcesses) {
    # Kill small PowerShell processes (likely orphaned)
    if ($proc.WorkingSet -lt 50MB) {
        if (-not $DryRun) {
            if (Safe-KillProcess -Process $proc -Description "PowerShell") {
                $killedPS++
            }
        } else {
            Write-Host "   🔍 DRY RUN: Would kill PowerShell PID $($proc.Id)" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "   ⚠️  KEPT: Large PowerShell PID $($proc.Id) ($(($proc.WorkingSet/1MB).ToString('F1')) MB)" -ForegroundColor Yellow
    }
}

$cleanupStats.PowerShellKilled = $killedPS
Write-Host "   📊 PowerShell processes killed: $killedPS" -ForegroundColor White

# 5. Clean other development processes
Write-Host "`n🟡 Cleaning other development processes..." -ForegroundColor Yellow

$otherProcesses = @()
$processesToCheck = @("code-tunnel", "cloudcode_cli")
$killedOther = 0

foreach ($processName in $processesToCheck) {
    $procs = Get-Process -Name $processName -ErrorAction SilentlyContinue
    foreach ($proc in $procs) {
        if (-not $DryRun) {
            if (Safe-KillProcess -Process $proc -Description $processName) {
                $killedOther++
            }
        } else {
            Write-Host "   🔍 DRY RUN: Would kill $processName PID $($proc.Id)" -ForegroundColor Yellow
        }
    }
}

$cleanupStats.OtherKilled = $killedOther
Write-Host "   📊 Other processes killed: $killedOther" -ForegroundColor White

# 6. Restart VS Code if requested
if ($RestartVSCode -and -not $DryRun) {
    Write-Host "`n🟡 Restarting VS Code..." -ForegroundColor Yellow
    try {
        # Find VS Code executable
        $vscodePath = Get-Command code -ErrorAction SilentlyContinue
        if ($vscodePath) {
            Write-Host "   🔄 Closing all VS Code instances..." -ForegroundColor Yellow
            Get-Process -Name "Code" -ErrorAction SilentlyContinue | Stop-Process -Force
            Start-Sleep -Seconds 3
            
            Write-Host "   🚀 Starting VS Code..." -ForegroundColor Yellow
            Start-Process -FilePath $vscodePath.Source -ArgumentList "." -WindowStyle Hidden
            $cleanupStats.VSCodeRestarted = $true
            Write-Host "   ✅ VS Code restarted successfully" -ForegroundColor Green
        } else {
            Write-Host "   ❌ VS Code executable not found in PATH" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   ❌ Failed to restart VS Code: $($_.Exception.Message)" -ForegroundColor Red
        $cleanupStats.Errors += "VS Code restart failed"
    }
}

# 7. Final summary
Write-Host "`n📊 CLEANUP SUMMARY:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

$totalKilled = $cleanupStats.NodeKilled + $cleanupStats.VSCodeKilled + $cleanupStats.PowerShellKilled + $cleanupStats.OtherKilled

Write-Host "✅ Node.js processes killed: $($cleanupStats.NodeKilled)" -ForegroundColor Green
Write-Host "✅ VS Code helpers killed: $($cleanupStats.VSCodeKilled)" -ForegroundColor Green
Write-Host "✅ PowerShell processes killed: $($cleanupStats.PowerShellKilled)" -ForegroundColor Green
Write-Host "✅ Other processes killed: $($cleanupStats.OtherKilled)" -ForegroundColor Green
Write-Host "📈 TOTAL PROCESSES KILLED: $totalKilled" -ForegroundColor Yellow

if ($cleanupStats.NPMCacheCleared) {
    Write-Host "✅ npm cache cleared" -ForegroundColor Green
}

if ($cleanupStats.VSCodeRestarted) {
    Write-Host "✅ VS Code restarted" -ForegroundColor Green
}

if ($cleanupStats.Errors.Count -gt 0) {
    Write-Host "❌ Errors encountered: $($cleanupStats.Errors.Count)" -ForegroundColor Red
    foreach ($error in $cleanupStats.Errors) {
        Write-Host "   • $error" -ForegroundColor Red
    }
}

# 8. Memory check after cleanup
Write-Host "`n💾 Memory usage after cleanup:" -ForegroundColor Cyan
try {
    $memoryInfo = Get-WmiObject -Class Win32_OperatingSystem
    $totalMemory = [math]::Round($memoryInfo.TotalVisibleMemorySize / 1MB, 2)
    $freeMemory = [math]::Round($memoryInfo.FreePhysicalMemory / 1MB, 2)
    $usedMemory = $totalMemory - $freeMemory
    $memoryUsagePercent = [math]::Round(($usedMemory / $totalMemory) * 100, 1)
    
    Write-Host "Used: $usedMemory GB of $totalMemory GB ($memoryUsagePercent%)" -ForegroundColor White
    
    if ($memoryUsagePercent -lt 70) {
        Write-Host "✅ Memory usage is now optimal!" -ForegroundColor Green
    }
}
catch {
    Write-Host "Could not retrieve memory information" -ForegroundColor Yellow
}

if ($DryRun) {
    Write-Host "`n🔍 DRY RUN COMPLETED - No actual changes made" -ForegroundColor Yellow
    Write-Host "Run without -DryRun to perform actual cleanup" -ForegroundColor Yellow
} else {
    Write-Host "`n🎉 CLEANUP COMPLETED SUCCESSFULLY!" -ForegroundColor Green
}

# 9. Next steps recommendations
Write-Host "`n💡 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "==============" -ForegroundColor Cyan
Write-Host "1. Monitor system for 5-10 minutes to ensure stability" -ForegroundColor White
Write-Host "2. If issues persist, run diagnostic script again" -ForegroundColor White
Write-Host "3. Consider restarting VS Code if you notice performance issues" -ForegroundColor White
Write-Host "4. Run 'npm cache verify' to check npm cache integrity" -ForegroundColor White
