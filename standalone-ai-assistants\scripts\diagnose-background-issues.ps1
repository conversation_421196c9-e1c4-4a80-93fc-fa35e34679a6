# Background Process Diagnostic Script
# ====================================

param(
    [switch]$Detailed,
    [switch]$SaveReport
)

# Set UTF-8 encoding
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🔍 Background Process Diagnostic Tool" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

$report = @{
    Timestamp = Get-Date
    NodeProcesses = @()
    VSCodeProcesses = @()
    PowerShellProcesses = @()
    OtherProcesses = @()
    SystemInfo = @{}
    Recommendations = @()
}

# Function to get detailed process info
function Get-DetailedProcessInfo {
    param($Process)
    try {
        $wmi = Get-WmiObject Win32_Process -Filter "ProcessId = $($Process.Id)" -ErrorAction SilentlyContinue
        return @{
            PID = $Process.Id
            Name = $Process.ProcessName
            MemoryMB = [math]::Round($Process.WorkingSet / 1MB, 1)
            CPU = $Process.CPU
            StartTime = $Process.StartTime
            CommandLine = if ($wmi) { $wmi.CommandLine } else { "Access Denied" }
            ParentPID = if ($wmi) { $wmi.ParentProcessId } else { $null }
        }
    }
    catch {
        return @{
            PID = $Process.Id
            Name = $Process.ProcessName
            MemoryMB = [math]::Round($Process.WorkingSet / 1MB, 1)
            Error = $_.Exception.Message
        }
    }
}

# 1. Analyze Node.js processes
Write-Host "`n🟡 Analyzing Node.js ecosystem..." -ForegroundColor Yellow

$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
$nodeCategories = @{
    Critical = @()
    Development = @()
    NPMCache = @()
    Unknown = @()
}

foreach ($proc in $nodeProcesses) {
    $info = Get-DetailedProcessInfo -Process $proc
    $report.NodeProcesses += $info
    
    $cmdLine = $info.CommandLine
    if ($cmdLine -match "anythingllm|ollama|n8n|ai-coordinator") {
        $nodeCategories.Critical += $info
        Write-Host "   🔒 CRITICAL: PID $($info.PID) - $($info.MemoryMB) MB" -ForegroundColor Green
    }
    elseif ($cmdLine -match "npm-cache|_npx") {
        $nodeCategories.NPMCache += $info
        Write-Host "   📦 NPM CACHE: PID $($info.PID) - $($info.MemoryMB) MB" -ForegroundColor Yellow
    }
    elseif ($cmdLine -match "nodemon|webpack|vite|dev-server|test") {
        $nodeCategories.Development += $info
        Write-Host "   🛠️  DEV TOOL: PID $($info.PID) - $($info.MemoryMB) MB" -ForegroundColor Cyan
    }
    else {
        $nodeCategories.Unknown += $info
        Write-Host "   ❓ UNKNOWN: PID $($info.PID) - $($info.MemoryMB) MB" -ForegroundColor Red
        if ($Detailed) {
            Write-Host "      CMD: $($cmdLine.Substring(0, [Math]::Min(80, $cmdLine.Length)))" -ForegroundColor Gray
        }
    }
}

Write-Host "   Summary: $($nodeCategories.Critical.Count) critical, $($nodeCategories.Development.Count) dev, $($nodeCategories.NPMCache.Count) npm-cache, $($nodeCategories.Unknown.Count) unknown" -ForegroundColor White

# 2. Analyze VS Code processes
Write-Host "`n🟡 Analyzing VS Code ecosystem..." -ForegroundColor Yellow

$codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
$codeCategories = @{
    Main = @()
    Extensions = @()
    Helpers = @()
    Large = @()
}

foreach ($proc in $codeProcesses) {
    $info = Get-DetailedProcessInfo -Process $proc
    $report.VSCodeProcesses += $info
    
    if ($info.MemoryMB -gt 200) {
        $codeCategories.Large += $info
        Write-Host "   🐘 LARGE: PID $($info.PID) - $($info.MemoryMB) MB" -ForegroundColor Red
    }
    elseif ($info.MemoryMB -gt 100) {
        $codeCategories.Main += $info
        Write-Host "   🏠 MAIN: PID $($info.PID) - $($info.MemoryMB) MB" -ForegroundColor Green
    }
    elseif ($info.MemoryMB -gt 50) {
        $codeCategories.Extensions += $info
        Write-Host "   🧩 EXTENSION: PID $($info.PID) - $($info.MemoryMB) MB" -ForegroundColor Cyan
    }
    else {
        $codeCategories.Helpers += $info
        Write-Host "   🔧 HELPER: PID $($info.PID) - $($info.MemoryMB) MB" -ForegroundColor Yellow
    }
}

$totalVSCodeMemory = ($codeProcesses | Measure-Object WorkingSet -Sum).Sum / 1MB
Write-Host "   Summary: $($codeProcesses.Count) processes using $([math]::Round($totalVSCodeMemory, 1)) MB total" -ForegroundColor White

# 3. Analyze PowerShell processes
Write-Host "`n🟡 Analyzing PowerShell processes..." -ForegroundColor Yellow

$psProcesses = Get-Process -Name "powershell" -ErrorAction SilentlyContinue
$currentPID = $PID

foreach ($proc in $psProcesses) {
    $info = Get-DetailedProcessInfo -Process $proc
    $report.PowerShellProcesses += $info
    
    if ($proc.Id -eq $currentPID) {
        Write-Host "   🔒 CURRENT: PID $($info.PID) - $($info.MemoryMB) MB" -ForegroundColor Green
    }
    else {
        Write-Host "   ❌ EXTRA: PID $($info.PID) - $($info.MemoryMB) MB" -ForegroundColor Red
        if ($Detailed -and $info.CommandLine) {
            Write-Host "      CMD: $($info.CommandLine.Substring(0, [Math]::Min(80, $info.CommandLine.Length)))" -ForegroundColor Gray
        }
    }
}

# 4. Check system resources
Write-Host "`n🟡 Analyzing system resources..." -ForegroundColor Yellow

try {
    $memoryInfo = Get-WmiObject -Class Win32_OperatingSystem
    $totalMemory = [math]::Round($memoryInfo.TotalVisibleMemorySize / 1MB, 2)
    $freeMemory = [math]::Round($memoryInfo.FreePhysicalMemory / 1MB, 2)
    $usedMemory = $totalMemory - $freeMemory
    $memoryUsagePercent = [math]::Round(($usedMemory / $totalMemory) * 100, 1)
    
    $report.SystemInfo = @{
        TotalMemoryGB = $totalMemory
        UsedMemoryGB = $usedMemory
        FreeMemoryGB = $freeMemory
        MemoryUsagePercent = $memoryUsagePercent
    }
    
    Write-Host "   Memory: $usedMemory GB / $totalMemory GB ($memoryUsagePercent%)" -ForegroundColor White
    
    if ($memoryUsagePercent -gt 80) {
        Write-Host "   ⚠️  HIGH MEMORY USAGE!" -ForegroundColor Red
        $report.Recommendations += "High memory usage detected - consider closing unnecessary processes"
    }
}
catch {
    Write-Host "   ❌ Could not retrieve memory information" -ForegroundColor Red
}

# 5. Generate recommendations
Write-Host "`n💡 RECOMMENDATIONS:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

if ($nodeCategories.NPMCache.Count -gt 5) {
    $rec = "Too many npm-cache processes ($($nodeCategories.NPMCache.Count)) - consider clearing npm cache"
    Write-Host "🔧 $rec" -ForegroundColor Yellow
    $report.Recommendations += $rec
}

if ($nodeCategories.Unknown.Count -gt 3) {
    $rec = "Multiple unknown Node.js processes ($($nodeCategories.Unknown.Count)) - investigate their purpose"
    Write-Host "🔍 $rec" -ForegroundColor Yellow
    $report.Recommendations += $rec
}

if ($codeCategories.Helpers.Count -gt 10) {
    $rec = "Too many VS Code helper processes ($($codeCategories.Helpers.Count)) - restart VS Code"
    Write-Host "🔄 $rec" -ForegroundColor Yellow
    $report.Recommendations += $rec
}

if ($codeCategories.Large.Count -gt 2) {
    $rec = "Multiple large VS Code processes ($($codeCategories.Large.Count)) - check for memory leaks"
    Write-Host "🐘 $rec" -ForegroundColor Yellow
    $report.Recommendations += $rec
}

if ($psProcesses.Count -gt 3) {
    $rec = "Too many PowerShell processes ($($psProcesses.Count)) - close unnecessary terminals"
    Write-Host "💻 $rec" -ForegroundColor Yellow
    $report.Recommendations += $rec
}

# 6. Suggested actions
Write-Host "`n🚀 SUGGESTED ACTIONS:" -ForegroundColor Green
Write-Host "====================" -ForegroundColor Green

Write-Host "1. Run cleanup script:" -ForegroundColor White
Write-Host "   .\cleanup-processes-fixed.ps1 -DryRun" -ForegroundColor Gray

Write-Host "2. Clear npm cache:" -ForegroundColor White
Write-Host "   npm cache clean --force" -ForegroundColor Gray

Write-Host "3. Restart VS Code if too many helpers" -ForegroundColor White

Write-Host "4. Close unnecessary PowerShell windows" -ForegroundColor White

# Save report if requested
if ($SaveReport) {
    $reportPath = "logs\background-process-diagnostic-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
    $reportDir = Split-Path $reportPath -Parent
    if (-not (Test-Path $reportDir)) {
        New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
    }
    
    $report | ConvertTo-Json -Depth 10 | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "`n💾 Report saved to: $reportPath" -ForegroundColor Cyan
}

Write-Host "`n✅ Diagnostic completed!" -ForegroundColor Green
