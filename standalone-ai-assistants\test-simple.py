#!/usr/bin/env python3
"""
اختبار بسيط للمساعدين المعزولين
"""

import os
import sys
from pathlib import Path

def test_structure():
    """اختبار هيكل المجلد"""
    print("🔍 اختبار هيكل المجلد...")
    
    required_items = [
        "README.md",
        "shared-memory/",
        "gemini-interface/",
        "agents-interface/",
        "scripts/",
        "logs/"
    ]
    
    found = 0
    for item in required_items:
        if Path(item).exists():
            print(f"✅ {item}")
            found += 1
        else:
            print(f"❌ {item}")
    
    print(f"\n📊 النتيجة: {found}/{len(required_items)} عنصر موجود")
    return found >= len(required_items) * 0.8

def test_memory():
    """اختبار الذاكرة"""
    print("\n🧠 اختبار الذاكرة...")
    
    memory_file = Path("shared-memory/project-knowledge.json")
    if memory_file.exists():
        print("✅ ملف الذاكرة موجود")
        try:
            import json
            with open(memory_file, 'r', encoding='utf-8') as f:
                memory = json.load(f)
            print("✅ ملف الذاكرة صالح")
            print(f"📁 المشروع: {memory.get('system_info', {}).get('name', 'غير محدد')}")
            return True
        except Exception as e:
            print(f"❌ خطأ في قراءة الذاكرة: {str(e)}")
            return False
    else:
        print("❌ ملف الذاكرة غير موجود")
        return False

def test_gemini_interface():
    """اختبار واجهة Gemini"""
    print("\n🧠 اختبار واجهة Gemini...")
    
    gemini_file = Path("gemini-interface/gemini-connector.py")
    if gemini_file.exists():
        print("✅ ملف واجهة Gemini موجود")
        return True
    else:
        print("❌ ملف واجهة Gemini غير موجود")
        return False

def main():
    """الاختبار الرئيسي"""
    # تغيير المجلد
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("🤖 اختبار بسيط للمساعدين المعزولين")
    print("=" * 50)
    
    # تشغيل الاختبارات
    tests = [
        ("هيكل المجلد", test_structure),
        ("الذاكرة المشتركة", test_memory),
        ("واجهة Gemini", test_gemini_interface)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    # النتيجة النهائية
    print(f"\n📊 النتيجة النهائية:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"📈 النسبة: {(passed/total)*100:.1f}%")
    
    if passed >= total * 0.8:
        print("\n🎉 النظام المعزول جاهز للاستخدام!")
        print("💡 يمكنك الآن استخدام المساعدين بأمان")
    else:
        print("\n⚠️ النظام يحتاج إلى إصلاحات")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
